import { CryptoIndicatorApiClient } from '@/generated';

/**
 * Singleton API Client Manager
 * Implements the Singleton pattern to ensure only one API client instance exists
 * Based on design patterns research from Context7
 */
class ApiClientSingleton {
  private static instance: CryptoIndicatorApiClient | null = null;

  /**
   * Private constructor to prevent direct instantiation
   */
  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get the singleton instance of the API client
   * Creates the instance only once and returns the same instance on subsequent calls
   */
  public static getInstance(): CryptoIndicatorApiClient {
    if (ApiClientSingleton.instance === null) {
      ApiClientSingleton.instance = new CryptoIndicatorApiClient();
    }
    return ApiClientSingleton.instance;
  }

  /**
   * Reset the singleton instance (useful for testing)
   */
  public static resetInstance(): void {
    ApiClientSingleton.instance = null;
  }
}

export { ApiClientSingleton };
