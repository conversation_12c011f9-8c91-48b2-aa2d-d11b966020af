// Asset types
export type AssetType = 'crypto' | 'stock';

// Unified column types (identical for both crypto and stock)
export type SortColumn = 'symbol' | 'usdPrice' | 'marketCap' | 'usdSignal' | 'btcPrice' | 'btcSignal';
export type SortDirection = 'asc' | 'desc' | null;
export type SignalColor = 'gold' | 'blue' | 'gray' | 'all';

// Unified configuration interfaces
export interface SortConfig {
  column: SortColumn | null;
  direction: SortDirection;
}

export interface FilterConfig {
  symbolSearch: string;
  usdSignal: SignalColor;
  btcSignal: SignalColor;
}

// Legacy type aliases for backward compatibility (will be removed)
export type StockSortColumn = SortColumn;
export type StockSortConfig = SortConfig;
export type StockFilterConfig = FilterConfig;


