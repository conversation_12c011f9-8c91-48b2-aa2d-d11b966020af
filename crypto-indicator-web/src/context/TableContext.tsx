import React, { createContext, useEffect, useMemo, useReducer } from 'react';

import { useTableDataManager } from '@/hooks/useTableDataManager';

import type {
  TableAction,
  TableActions,
  TableContextValue,
  TableProviderProps,
  TableState,
} from './TableTypes';
import type { FilterConfig, SortColumn, SortDirection, StockFilterConfig, StockSortColumn } from '@/types/table';
import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from '@/generated';

// Default filter configurations
const DEFAULT_CRYPTO_FILTERS: FilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

const DEFAULT_STOCK_FILTERS: StockFilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

// Initial state factory
const createInitialState = (assetType: 'crypto' | 'stock'): TableState => ({
  rawData: [],
  data: [],
  btcStatistics: [],
  loading: false,
  error: null,
  assetType,
  filterConfig: assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS,
  hasActiveFilters: false,
  filteredCount: 0,
  totalCount: 0,
  sortConfig: {
    column: null,
    direction: null,
  },
});

// Table Reducer
// eslint-disable-next-line max-lines-per-function
const tableReducer = (state: TableState, action: TableAction): TableState => {
  switch (action.type) {
    case 'SET_DATA': {
      const filteredData = applyFilteringAndSorting(
        action.payload.data,
        action.payload.btcData,
        state.filterConfig,
        state.sortConfig,
        state.assetType
      );

      return {
        ...state,
        rawData: action.payload.data,
        data: filteredData,
        btcStatistics: action.payload.btcData,
        totalCount: action.payload.totalCount,
        filteredCount: filteredData.length,
        loading: false,
        error: null,
      };
    }
    
    case 'SET_LOADING': {
      return {
        ...state,
        loading: action.payload,
        ...(action.payload && { error: null }),
      };
    }
    
    case 'SET_ERROR': {
      return {
        ...state,
        error: action.payload,
        loading: false,
      };
    }
    
    case 'SET_FILTER': {
      const newFilterConfig = { ...state.filterConfig, ...action.payload };
      const filteredData = applyFilteringAndSorting(
        state.rawData,
        state.btcStatistics,
        newFilterConfig,
        state.sortConfig,
        state.assetType
      );

      // Check if filters are active
      const defaultFilters = state.assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      const hasActiveFilters = Object.keys(newFilterConfig).some(key =>
        newFilterConfig[key as keyof typeof newFilterConfig] !== defaultFilters[key as keyof typeof defaultFilters]
      );

      return {
        ...state,
        filterConfig: newFilterConfig,
        data: filteredData,
        filteredCount: filteredData.length,
        hasActiveFilters,
      };
    }
    
    case 'SET_SORT': {
      const newSortConfig = {
        column: action.payload.column,
        direction: action.payload.direction,
      };

      const filteredData = applyFilteringAndSorting(
        state.rawData,
        state.btcStatistics,
        state.filterConfig,
        newSortConfig,
        state.assetType
      );

      return {
        ...state,
        sortConfig: newSortConfig,
        data: filteredData,
        filteredCount: filteredData.length,
      };
    }
    
    case 'SET_ASSET_TYPE': {
      const newFilterConfig = action.payload === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      const newSortConfig = { column: null, direction: null };

      const filteredData = applyFilteringAndSorting(
        state.rawData,
        state.btcStatistics,
        newFilterConfig,
        newSortConfig,
        action.payload
      );

      return {
        ...state,
        assetType: action.payload,
        filterConfig: newFilterConfig,
        sortConfig: newSortConfig,
        data: filteredData,
        filteredCount: filteredData.length,
        hasActiveFilters: false,
      };
    }
    
    case 'SET_FILTERED_COUNT': {
      return {
        ...state,
        filteredCount: action.payload,
      };
    }
    
    case 'SET_ACTIVE_FILTERS': {
      return {
        ...state,
        hasActiveFilters: action.payload,
      };
    }
    
    default: {
      return state;
    }
  }
};

// Helper function to apply filtering and sorting to data
const applyFilteringAndSorting = (
  rawData: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
  btcData: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
  filterConfig: FilterConfig | StockFilterConfig,
  sortConfig: { column: SortColumn | StockSortColumn | null; direction: SortDirection },
  assetType: 'crypto' | 'stock'
) => {
  let filteredData = rawData;

  // Apply filtering
  if (assetType === 'crypto') {
    filteredData = applyFilters(
      rawData as CryptoCurrencyStatisticsDto[],
      btcData as CryptoCurrencyStatisticsDto[],
      filterConfig as FilterConfig,
      (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) =>
        btcStats.find(stat => stat.symbol === symbol)?.indicatorValues?.[0]
    );
  } else {
    filteredData = applyStockFilters(
      rawData as StockStatisticsDto[],
      btcData as StockStatisticsDto[],
      filterConfig as StockFilterConfig
    );
  }

  // Apply sorting
  if (sortConfig.column && sortConfig.direction) {
    if (assetType === 'crypto') {
      filteredData = applySorting(
        filteredData as CryptoCurrencyStatisticsDto[],
        btcData as CryptoCurrencyStatisticsDto[],
        {
          column: sortConfig.column as SortColumn,
          direction: sortConfig.direction,
        },
        (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) =>
          btcStats.find(stat => stat.symbol === symbol)?.indicatorValues?.[0]
      );
    } else {
      filteredData = applyStockSorting(
        filteredData as StockStatisticsDto[],
        btcData as StockStatisticsDto[],
        {
          column: sortConfig.column as StockSortColumn,
          direction: sortConfig.direction,
        }
      );
    }
  }

  return filteredData;
};

// Create Context
const TableContext = createContext<TableContextValue | null>(null);



// Table Provider Component
// eslint-disable-next-line max-lines-per-function
export const TableProvider: React.FC<TableProviderProps> = ({
  children,
  assetType,
  onSignalClick: passedOnSignalClick,
  onRefresh: passedOnRefresh,
  data: passedData,
  btcStatistics: passedBtcStatistics,
  loading: passedLoading,
  error: passedError,
  totalCount: passedTotalCount,
}) => {
  const [state, dispatch] = useReducer(tableReducer, createInitialState(assetType));

  // If data is passed, use it; otherwise fall back to useTableDataManager
  const shouldUsePassedData = passedData !== undefined;

  const {
    processedData: defaultProcessedData,
    btcStatistics: defaultBtcStatistics,
    totalCount: defaultTotalCount,
    loading: defaultLoading,
    error: defaultError,
    onSignalClick: defaultOnSignalClick,
    onRefresh: defaultOnRefresh,
    formatDate,
    findBtcDataForSymbol,
  } = useTableDataManager({ assetType });

  // Use passed data/handlers if available, otherwise fall back to defaults
  const processedData = shouldUsePassedData ? (passedData ?? []) : defaultProcessedData;
  const btcStatistics = shouldUsePassedData ? (passedBtcStatistics ?? []) : defaultBtcStatistics;
  const totalCount = shouldUsePassedData ? (passedTotalCount ?? 0) : defaultTotalCount;
  const loading = shouldUsePassedData ? (passedLoading ?? false) : defaultLoading;
  const error = shouldUsePassedData ? (passedError ?? null) : defaultError;
  const onSignalClick = passedOnSignalClick ?? defaultOnSignalClick;
  const onRefresh = passedOnRefresh ?? defaultOnRefresh;

  // Update context state when data changes
  useEffect(() => {
    dispatch({
      type: 'SET_DATA',
      payload: {
        data: processedData,
        btcData: btcStatistics,
        totalCount,
      },
    });
  }, [processedData, btcStatistics, totalCount]);

  useEffect(() => {
    dispatch({ type: 'SET_LOADING', payload: loading });
  }, [loading]);

  useEffect(() => {
    dispatch({ type: 'SET_ERROR', payload: error });
  }, [error]);

  useEffect(() => {
    dispatch({ type: 'SET_ASSET_TYPE', payload: assetType });
  }, [assetType]);

  // Memoized actions to prevent unnecessary re-renders
  const actions = useMemo<TableActions>(() => ({
    onSignalClick,
    onRefresh,
    
    onSort: (column: SortColumn | StockSortColumn) => {
      const currentDirection = state.sortConfig.column === column ? state.sortConfig.direction : null;
      let newDirection: SortDirection;
      
      if (currentDirection === null) {
        newDirection = 'desc';
      } else if (currentDirection === 'desc') {
        newDirection = 'asc';
      } else {
        newDirection = null;
      }
      
      dispatch({
        type: 'SET_SORT',
        payload: {
          column: newDirection === null ? null : column,
          direction: newDirection
        },
      });
    },
    
    getSortDirection: (column: SortColumn | StockSortColumn) => {
      return state.sortConfig.column === column ? state.sortConfig.direction : null;
    },
    
    onFilterChange: (filters: Partial<FilterConfig | StockFilterConfig>) => {
      dispatch({ type: 'SET_FILTER', payload: filters });
    },
    
    onClearFilters: () => {
      const defaultFilters = assetType === 'crypto' ? DEFAULT_CRYPTO_FILTERS : DEFAULT_STOCK_FILTERS;
      dispatch({ type: 'SET_FILTER', payload: defaultFilters });
    },
    
    formatDate,
    findBtcDataForSymbol,
  }), [
    onSignalClick,
    onRefresh,
    formatDate,
    findBtcDataForSymbol,
    state.sortConfig,
    assetType,
  ]);

  const contextValue = useMemo<TableContextValue>(() => ({
    state,
    dispatch,
    actions,
  }), [state, actions]);

  return (
    <TableContext.Provider value={contextValue}>
      {children}
    </TableContext.Provider>
  );
};

export { TableContext };


export {type TableState, type TableAction, type TableActions, type TableContextValue} from './TableTypes';
