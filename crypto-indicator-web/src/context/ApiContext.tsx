import React, { createContext, ReactNode, useMemo } from 'react';

import { CryptoIndicatorApiClient } from '@/generated';
import { ApiClientSingleton } from '@/services/ApiClientSingleton';
import { CryptoDataService } from '@/services/CryptoDataService';

interface ApiContextType {
  cryptoDataService: CryptoDataService;
  apiClient: CryptoIndicatorApiClient;
}

const ApiContext = createContext<ApiContextType | null>(null);

interface ApiProviderProps {
  children: ReactNode;
}

/**
 * API Context Provider
 * Implements dependency injection for services using Singleton pattern
 * Based on design patterns research from Context7
 */
export const ApiProvider: React.FC<ApiProviderProps> = ({ children }) => {
  const contextValue = useMemo(() => {
    // Use singleton instance to ensure stable API client across renders
    const apiClient = ApiClientSingleton.getInstance();
    const cryptoDataService = new CryptoDataService(apiClient);

    return {
      cryptoDataService,
      apiClient,
    };
  }, []); // Empty dependency array since singleton ensures stable instance

  return (
    <ApiContext.Provider value={contextValue}>
      {children}
    </ApiContext.Provider>
  );
};

/**
 * Hook to consume the API context
 * Returns the API context value or null if not within an ApiProvider
 */
export const useApiContext = (): ApiContextType | null => {
  return React.useContext(ApiContext);
};
