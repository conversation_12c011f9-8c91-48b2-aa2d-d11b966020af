import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';
import type {
  FilterConfig,
  SortColumn,
  SortDirection,
  StockFilterConfig,
  StockSortColumn,
} from '@/types/table';
import type { Dispatch,ReactNode } from 'react';

// Table State Interface
export interface TableState {
  // Data
  rawData: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[]; // Original unfiltered data
  data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[]; // Filtered and sorted data
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  loading: boolean;
  error: string | null;

  // Asset type
  assetType: 'crypto' | 'stock';

  // Filtering
  filterConfig: FilterConfig | StockFilterConfig;
  hasActiveFilters: boolean;
  filteredCount: number;
  totalCount: number;

  // Sorting
  sortConfig: {
    column: SortColumn | StockSortColumn | null;
    direction: SortDirection;
  };
}

// Table Actions
export type TableAction =
  | { type: 'SET_DATA'; payload: {
      data: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
      btcData: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
      totalCount: number;
    } }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_FILTER'; payload: Partial<FilterConfig | StockFilterConfig> }
  | { type: 'SET_SORT'; payload: { column: SortColumn | StockSortColumn | null; direction: SortDirection } }
  | { type: 'SET_ASSET_TYPE'; payload: 'crypto' | 'stock' }
  | { type: 'SET_FILTERED_COUNT'; payload: number }
  | { type: 'SET_ACTIVE_FILTERS'; payload: boolean };

// Table Actions Interface
export interface TableActions {
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;
  onSort: (column: SortColumn | StockSortColumn) => void;
  getSortDirection: (column: SortColumn | StockSortColumn) => SortDirection;
  onFilterChange: (filters: Partial<FilterConfig | StockFilterConfig>) => void;
  onClearFilters: () => void;
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

// Context Value Interface
export interface TableContextValue {
  state: TableState;
  dispatch: Dispatch<TableAction>;
  actions: TableActions;
}

// Table Provider Props
export interface TableProviderProps {
  children: ReactNode;
  assetType: 'crypto' | 'stock';
  onSignalClick?: (symbol: string, currency: string) => Promise<void>;
  onRefresh?: () => void;
  data?: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  btcStatistics?: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[];
  loading?: boolean;
  error?: string | null;
  totalCount?: number;
}
