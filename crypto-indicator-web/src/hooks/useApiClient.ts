import { useApiContext } from '@/context/ApiContext';
import { CryptoIndicatorApiClient } from '@/generated';
import { ApiClientSingleton } from '@/services/ApiClientSingleton';

/**
 * Hook for managing API client instance
 * Implements dependency injection pattern using Singleton + Context Provider
 * Based on design patterns research from Context7
 */
export const useApiClient = (): CryptoIndicatorApiClient => {
  // Try to get from context first (if ApiProvider is used)
  const context = useApiContext();
  if (context?.apiClient) {
    return context.apiClient;
  }

  // Fallback to singleton pattern for components not wrapped in ApiProvider
  return ApiClientSingleton.getInstance();
};
