import { useCallback, useMemo, useState } from 'react';

import type { FilterConfig, SignalColor } from '@/types/table';

// Generic interface for filtering hook return
interface UseAssetFilteringReturn {
  filterConfig: FilterConfig;
  updateSymbolSearch: (search: string) => void;
  updateUsdSignal: (signal: SignalColor) => void;
  updateBtcSignal: (signal: SignalColor) => void;
  clearFilters: () => void;
  hasActiveFilters: boolean;
}

const INITIAL_FILTER_CONFIG: FilterConfig = {
  symbolSearch: '',
  usdSignal: 'all',
  btcSignal: 'all',
};

/**
 * Generic hook for asset filtering (crypto or stock)
 * Replaces useFiltering and useStockFiltering with a single generic implementation
 * Since both crypto and stock use identical filter structure and logic, no asset type needed
 */
export const useAssetFiltering = (
  initialConfig: FilterConfig = INITIAL_FILTER_CONFIG
): UseAssetFilteringReturn => {
  const [filterConfig, setFilterConfig] = useState<FilterConfig>(initialConfig);

  const updateSymbolSearch = useCallback((search: string): void => {
    setFilterConfig((prev) => ({ ...prev, symbolSearch: search }));
  }, []);

  const updateUsdSignal = useCallback((signal: SignalColor): void => {
    setFilterConfig((prev) => ({ ...prev, usdSignal: signal }));
  }, []);

  const updateBtcSignal = useCallback((signal: SignalColor): void => {
    setFilterConfig((prev) => ({ ...prev, btcSignal: signal }));
  }, []);

  const clearFilters = useCallback((): void => {
    setFilterConfig(INITIAL_FILTER_CONFIG);
  }, []);

  const hasActiveFilters = useMemo((): boolean => {
    return (
      filterConfig.symbolSearch.trim() !== '' ||
      filterConfig.usdSignal !== 'all' ||
      filterConfig.btcSignal !== 'all'
    );
  }, [filterConfig]);

  return {
    filterConfig,
    updateSymbolSearch,
    updateUsdSignal,
    updateBtcSignal,
    clearFilters,
    hasActiveFilters,
  };
};

/**
 * Convenience aliases for backward compatibility
 */
export const useFiltering = useAssetFiltering;
export const useStockFiltering = useAssetFiltering;
