import { useContext } from 'react';

import { TableContext } from '@/context/TableContext';

import type { TableActions, TableContextValue,TableState } from '@/context/TableContext';

/**
 * Hook to access the complete table context
 * Throws error if used outside TableProvider
 */
export const useTableContext = (): TableContextValue => {
  const context = useContext(TableContext);
  
  if (!context) {
    throw new Error(
      'useTableContext must be used within a TableProvider. ' +
      'Make sure your component is wrapped with <TableProvider>.'
    );
  }
  
  return context;
};

/**
 * Hook to access only the table state
 * More efficient when you only need to read state
 */
export const useTableState = (): TableState => {
  const { state } = useTableContext();
  return state;
};

/**
 * Hook to access only the table actions
 * More efficient when you only need actions
 */
export const useTableActions = (): TableActions => {
  const { actions } = useTableContext();
  return actions;
};

/**
 * Hook to access only the dispatch function
 * For components that need to dispatch actions directly
 */
export const useTableDispatch = () => {
  const { dispatch } = useTableContext();
  return dispatch;
};

/**
 * Hook to access specific state properties
 * Useful for components that only need specific data
 */
export const useTableData = () => {
  const { state } = useTableContext();
  return {
    data: state.data,
    btcStatistics: state.btcStatistics,
    loading: state.loading,
    error: state.error,
    totalCount: state.totalCount,
    filteredCount: state.filteredCount,
  };
};

/**
 * Hook to access filtering-related state and actions
 */
export const useTableFiltering = () => {
  const { state, actions } = useTableContext();
  return {
    filterConfig: state.filterConfig,
    hasActiveFilters: state.hasActiveFilters,
    onFilterChange: actions.onFilterChange,
    onClearFilters: actions.onClearFilters,
  };
};

/**
 * Hook to access sorting-related state and actions
 */
export const useTableSorting = () => {
  const { state, actions } = useTableContext();
  return {
    sortConfig: state.sortConfig,
    onSort: actions.onSort,
    getSortDirection: actions.getSortDirection,
  };
};

/**
 * Hook to access asset type and related utilities
 */
export const useTableAssetType = () => {
  const { state, actions } = useTableContext();
  return {
    assetType: state.assetType,
    formatDate: actions.formatDate,
    findBtcDataForSymbol: actions.findBtcDataForSymbol,
  };
};

/**
 * Hook to access interaction handlers
 */
export const useTableInteractions = () => {
  const { actions } = useTableContext();
  return {
    onSignalClick: actions.onSignalClick,
    onRefresh: actions.onRefresh,
  };
};
