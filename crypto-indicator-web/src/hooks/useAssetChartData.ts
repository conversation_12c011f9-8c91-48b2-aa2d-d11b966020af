import { useCallback, useState } from 'react';

import { defaultApiClient } from '@/generated';
import { StockDataService } from '@/services/StockDataService';

import { useApiClient } from './useApiClient';

import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from '@/generated';
import type { AssetType } from '@/types/table';

// Union type for chart data
export type AssetChartData = CryptoCurrencyStatisticsDto | StockStatisticsDto;

// Generic interface for chart data hook return
interface UseAssetChartDataReturn<T extends AssetChartData> {
  chartData: T | null;
  chartLoading: boolean;
  chartError: string | null;
  fetchChartData: (symbol: string, conversionCurrency: string) => Promise<void>;
  clearChartData: () => void;
}

// Service instances
const stockDataService = new StockDataService(defaultApiClient);

/**
 * Generic hook for fetching chart data (crypto or stock)
 * Replaces useChartData and useStockChartData with a single generic implementation
 */
export function useAssetChartData<T extends AssetChartData>(
  assetType: AssetType
): UseAssetChartDataReturn<T> {
  const [chartData, setChartData] = useState<T | null>(null);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState<string | null>(null);
  const apiClient = useApiClient();

  const fetchChartData = useCallback(async (
    symbol: string, 
    conversionCurrency: string
  ): Promise<void> => {
    setChartLoading(true);
    setChartError(null);

    try {
      const result: T = assetType === 'crypto'
        ? await apiClient.CryptoStatisticsController_getCryptoIndicators(symbol, conversionCurrency) as T
        : await stockDataService.getIndicators(symbol, conversionCurrency) as T;
      
      setChartData(result);
    } catch (error_) {
      const errorMessage = error_ instanceof Error 
        ? error_.message 
        : `Failed to fetch ${assetType} chart data`;
      setChartError(errorMessage);
      // eslint-disable-next-line no-console
      console.error(`Error fetching ${assetType} chart data:`, error_);
    } finally {
      setChartLoading(false);
    }
  }, [assetType, apiClient]);

  const clearChartData = useCallback((): void => {
    setChartData(null);
    setChartError(null);
  }, []);

  return {
    chartData,
    chartLoading,
    chartError,
    fetchChartData,
    clearChartData,
  };
}

/**
 * Convenience hooks for backward compatibility
 */
export const useChartData = () => 
  useAssetChartData<CryptoCurrencyStatisticsDto>('crypto');

export const useStockChartData = () => 
  useAssetChartData<StockStatisticsDto>('stock');
