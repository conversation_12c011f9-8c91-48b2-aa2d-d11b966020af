import { useCallback, useState } from 'react';

import { useApiClient } from './useApiClient';
import { defaultApiClient } from '@/generated';
import { StockDataService } from '@/services/StockDataService';

import type { CryptoCurrencyStatisticsDto, StockStatisticsDto } from '@/generated';
import type { AssetType } from '@/types/table';

// Union type for asset statistics
export type AssetStatisticsDto = CryptoCurrencyStatisticsDto | StockStatisticsDto;

// Generic interface for asset data hook return
interface UseAssetDataReturn<T extends AssetStatisticsDto> {
  data: T[];
  loading: boolean;
  error: string | null;
  fetchData: () => Promise<void>;
  refetch: () => Promise<void>;
}

// Service instances
const stockDataService = new StockDataService(defaultApiClient);

/**
 * Generic hook for fetching asset data (crypto or stock)
 * Replaces useCryptoData and useStockData with a single generic implementation
 */
export function useAssetData<T extends AssetStatisticsDto>(
  assetType: AssetType
): UseAssetDataReturn<T> {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const apiClient = useApiClient();

  const fetchData = useCallback(async (): Promise<void> => {
    setLoading(true);
    setError(null);

    try {
      let result: T[];
      
      if (assetType === 'crypto') {
        result = await apiClient.CryptoStatisticsController_getCryptoStatistics() as T[];
      } else {
        result = await stockDataService.getStatistics() as T[];
      }
      
      setData(result);
    } catch (error_) {
      const errorMessage = error_ instanceof Error 
        ? error_.message 
        : `Failed to fetch ${assetType} data`;
      setError(errorMessage);
      // eslint-disable-next-line no-console
      console.error(`Error fetching ${assetType} data:`, error_);
    } finally {
      setLoading(false);
    }
  }, [assetType, apiClient]);

  return {
    data,
    loading,
    error,
    fetchData,
    refetch: fetchData,
  };
}

/**
 * Convenience hooks for backward compatibility
 */
export const useCryptoData = () => 
  useAssetData<CryptoCurrencyStatisticsDto>('crypto');

export const useStockData = () => 
  useAssetData<StockStatisticsDto>('stock');
