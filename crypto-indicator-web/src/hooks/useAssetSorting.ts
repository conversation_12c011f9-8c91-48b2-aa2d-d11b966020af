import { useCallback, useState } from 'react';

import type { SortColumn, SortConfig, SortDirection } from '@/types/table';

// Generic interface for sorting hook return
interface UseAssetSortingReturn {
  sortConfig: SortConfig;
  handleSort: (column: SortColumn) => void;
  getSortDirection: (column: SortColumn) => SortDirection;
  clearSort: () => void;
}

const INITIAL_SORT_CONFIG: SortConfig = {
  column: null,
  direction: null,
};

/**
 * Generic hook for asset sorting (crypto or stock)
 * Replaces useSorting and useStockSorting with a single generic implementation
 * Since both crypto and stock use identical column types and logic, no asset type needed
 */
export const useAssetSorting = (
  initialConfig: SortConfig = INITIAL_SORT_CONFIG
): UseAssetSortingReturn => {
  const [sortConfig, setSortConfig] = useState<SortConfig>(initialConfig);

  const handleSort = useCallback((column: SortColumn): void => {
    setSortConfig((prevConfig) => {
      // If clicking the same column, cycle through: asc -> desc -> null
      if (prevConfig.column === column) {
        if (prevConfig.direction === 'asc') {
          return { column, direction: 'desc' };
        } else if (prevConfig.direction === 'desc') {
          return { column: null, direction: null };
        }
      }
      
      // If clicking a different column or no current sort, start with asc
      return { column, direction: 'asc' };
    });
  }, []);

  const clearSort = useCallback((): void => {
    setSortConfig(INITIAL_SORT_CONFIG);
  }, []);

  const getSortDirection = useCallback((column: SortColumn): SortDirection => {
    return sortConfig.column === column ? sortConfig.direction : null;
  }, [sortConfig]);

  return {
    sortConfig,
    handleSort,
    clearSort,
    getSortDirection,
  };
};

/**
 * Convenience aliases for backward compatibility
 */
export const useSorting = useAssetSorting;
export const useStockSorting = useAssetSorting;
