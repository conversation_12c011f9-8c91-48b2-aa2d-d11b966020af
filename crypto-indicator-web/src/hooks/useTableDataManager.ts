import { useCallback, useEffect, useMemo, useState } from 'react';

import { useAssetData } from '@/hooks/useAssetData';
import { useAssetChartData } from '@/hooks/useAssetChartData';
import { useAssetFiltering } from '@/hooks/useAssetFiltering';
import { useAssetSorting } from '@/hooks/useAssetSorting';
import {
  findBtcDataForSymbol,
  formatDate,
  processCryptoStatistics,
} from '@/utils/dataProcessors';
import { processStockStatistics } from '@/utils/stockDataProcessors';
import { applyAssetFilters } from '@/utils/assetTableFiltering';
import { applyAssetSorting } from '@/utils/assetTableSorting';

import type { CryptoCurrencyStatisticsDto, IndicatorValueDto, StockStatisticsDto } from '@/generated';
import type { AssetType } from '@/types/table';

interface UseTableDataManagerProps {
  assetType: AssetType;
}

interface TableDataManagerReturn {
  // Processed data
  processedData: any[];
  btcStatistics: any[];
  totalCount: number;
  filteredCount: number;
  loading: boolean;
  error: string | null;

  // Chart data
  chartData: unknown;
  showChart: boolean;
  setShowChart: (show: boolean) => void;

  // Actions
  onSignalClick: (symbol: string, currency: string) => Promise<void>;
  onRefresh: () => void;

  // Utilities
  formatDate: (date?: string) => string;
  findBtcDataForSymbol: (
    btcStats: any[],
    symbol: string,
  ) => IndicatorValueDto | undefined;
}

/**
 * Unified hook that manages all table data logic for both crypto and stock
 * Now uses generic hooks to eliminate duplication
 */
export const useTableDataManager = ({
  assetType
}: UseTableDataManagerProps): TableDataManagerReturn => {
  const [showChart, setShowChart] = useState(false);

  // Generic hooks that work for both crypto and stock
  const {
    data: assetStatistics,
    loading,
    error,
    fetchData,
  } = useAssetData(assetType);

  const {
    chartData,
    fetchChartData,
  } = useAssetChartData(assetType);

  const {
    filterConfig,
  } = useAssetFiltering();

  const {
    sortConfig,
  } = useAssetSorting();

  // Process and filter data
  const processedData = useMemo(() => {
    if (assetStatistics === null || assetStatistics === undefined || assetStatistics.length === 0) {
      return [];
    }

    if (assetType === 'crypto') {
      const result = processCryptoStatistics(assetStatistics as CryptoCurrencyStatisticsDto[]);
      return result.usdStatistics;
    }
    const result = processStockStatistics(assetStatistics as StockStatisticsDto[]);
    return result.stockStatistics;
  }, [assetStatistics, assetType]);

  // BTC statistics (for both crypto and stocks)
  const btcStatistics = useMemo(() => {
    if (assetStatistics !== null && assetStatistics !== undefined && assetStatistics.length > 0) {
      if (assetType === 'crypto') {
        const result = processCryptoStatistics(assetStatistics as CryptoCurrencyStatisticsDto[]);
        return result.btcStatistics;
      }
      const result = processStockStatistics(assetStatistics as StockStatisticsDto[]);
      return result.btcStatistics;
    }
    return [];
  }, [assetStatistics, assetType]);

  const filteredData = useMemo(() => {
    if (processedData === null || processedData === undefined || processedData.length === 0) {
      return [];
    }

    // Apply filtering first
    const filtered = applyAssetFilters(
      processedData as any,
      btcStatistics as any,
      filterConfig
    );

    // Then apply sorting
    return applyAssetSorting(filtered, btcStatistics as any, sortConfig, assetType);
  }, [processedData, btcStatistics, filterConfig, sortConfig, assetType]);

  // Signal click handler
  const onSignalClick = useCallback(async (symbol: string, currency: string) => {
    try {
      await fetchChartData(symbol, currency);
      setShowChart(true);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Failed to handle signal click:', error);
    }
  }, [fetchChartData]);

  // Refresh handler
  const onRefresh = useCallback(() => {
    void fetchData();
  }, [fetchData]);

  // Initial data fetch effect
  useEffect(() => {
    onRefresh();
  }, [onRefresh]);

  // Auto-refresh effect
  useEffect(() => {
    const REFRESH_INTERVAL = 30_000; // 30 seconds
    const interval = setInterval(() => {
      onRefresh();
    }, REFRESH_INTERVAL);

    return () => { clearInterval(interval); };
  }, [onRefresh]);

  return {
    // Processed data
    processedData: filteredData,
    btcStatistics,
    totalCount: processedData.length,
    filteredCount: filteredData.length,
    loading,
    error,
    
    // Chart data
    chartData: chartData as unknown,
    showChart,
    setShowChart,
    
    // Actions
    onSignalClick,
    onRefresh,
    
    // Utilities
    formatDate,
    findBtcDataForSymbol,
  };
};
