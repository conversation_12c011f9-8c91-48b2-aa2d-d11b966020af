import React, { useState } from 'react';

import { useResponsiveLayout } from '@/hooks/useResponsiveLayout';

import { DesktopLayout } from './DesktopLayout';
import { MobileLayout } from './MobileLayout';
import { TabletLayout } from './TabletLayout';

// Component-specific styles
import '../../styles/layout/responsive-layout.css';

export const ResponsiveTableContainer: React.FC = () => {
  const { layoutType } = useResponsiveLayout();
  const [isFilterDrawerOpen, setIsFilterDrawerOpen] = useState(false);

  return (
    <div className={`responsive-table-container layout-${layoutType}`}>
      {layoutType === 'mobile' && (
        <MobileLayout
          isFilterDrawerOpen={isFilterDrawerOpen}
          onFilterDrawerToggle={setIsFilterDrawerOpen}
        />
      )}
      {layoutType === 'tablet' && <TabletLayout />}
      {layoutType === 'desktop' && <DesktopLayout />}
    </div>
  );
};
