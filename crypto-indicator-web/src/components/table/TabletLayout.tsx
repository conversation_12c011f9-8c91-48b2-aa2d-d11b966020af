import React from 'react';

import { Card } from '@/components/card/Card';
import { useTableAssetType, useTableData } from '@/hooks/useTableContext';

import { TableFilters } from './TableFilters';

import type { CryptoCurrencyStatisticsDto } from '@/generated';

export const TabletLayout: React.FC = () => {
  const { data, btcStatistics } = useTableData();
  const { findBtcDataForSymbol } = useTableAssetType();
  return (
    <div className="tablet-layout">
      <TableFilters />

      <div className="crypto-cards-grid">
        {data.map(item => {
          const btcData = findBtcDataForSymbol(btcStatistics, item.symbol);
          return (
            <Card
              key={item.symbol}
              crypto={item as CryptoCurrencyStatisticsDto}
              {...(btcData && { btcData })}
            />
          );
        })}
      </div>
    </div>
  );
};
