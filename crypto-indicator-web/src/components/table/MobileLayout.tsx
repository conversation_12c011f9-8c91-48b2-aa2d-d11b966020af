import React from 'react';

import { Card } from '@/components/card/Card';
import { FilterDrawer } from '@/components/filters/FilterDrawer';
import { FilterToggle } from '@/components/filters/FilterToggle';
import { useTableAssetType, useTableData } from '@/hooks/useTableContext';

import type { CryptoCurrencyStatisticsDto } from '@/generated';

interface MobileLayoutProps {
  isFilterDrawerOpen: boolean;
  onFilterDrawerToggle: (open: boolean) => void;
}

// Helper component for rendering cards
const CardsContainer: React.FC = () => {
  const { data, btcStatistics } = useTableData();
  const { findBtcDataForSymbol } = useTableAssetType();

  return (
    <div className="crypto-cards-container">
      {data.map(item => {
        const btcData = findBtcDataForSymbol(btcStatistics, item.symbol);
        return (
          <Card
            key={item.symbol}
            crypto={item as CryptoCurrencyStatisticsDto}
            {...(btcData && { btcData })}
          />
        );
      })}
    </div>
  );
};

export const MobileLayout: React.FC<MobileLayoutProps> = ({
  isFilterDrawerOpen,
  onFilterDrawerToggle,
}) => {
  return (
    <div className="mobile-layout">
      <FilterToggle
        onClick={() => {
          onFilterDrawerToggle(true);
        }}
      />

      <CardsContainer />

      <FilterDrawer
        isOpen={isFilterDrawerOpen}
        onClose={() => {
          onFilterDrawerToggle(false);
        }}
      />
    </div>
  );
};
