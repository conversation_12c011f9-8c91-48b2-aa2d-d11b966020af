import React from 'react';

import { CSS_CLASSES, UI_TEXT } from '@/constants/app';
import { useTableData, useTableInteractions } from '@/hooks/useTableContext';

export const TableControls: React.FC = () => {
  const { totalCount, loading } = useTableData();
  const { onRefresh } = useTableInteractions();

  return (
    <div className={CSS_CLASSES.STATS_INFO}>
      <div>
        <strong>{totalCount}</strong> {UI_TEXT.CRYPTOCURRENCIES}
      </div>
      <button onClick={onRefresh} disabled={loading}>
        {loading ? UI_TEXT.REFRESHING : UI_TEXT.REFRESH}
      </button>
    </div>
  );
};
