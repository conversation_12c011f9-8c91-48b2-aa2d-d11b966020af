import React from 'react';

import { useTableFiltering, useTableState } from '@/hooks/useTableContext';

import type { FilterConfig } from '@/types/table';

export const FilterDrawerContent: React.FC = () => {
  const { filterConfig, onFilterChange } = useTableFiltering();
  const { assetType } = useTableState();

  const handleUsdSignalChange = (signal: FilterConfig['usdSignal']) => {
    onFilterChange({ usdSignal: signal });
  };

  const handleBtcSignalChange = (signal: FilterConfig['btcSignal']) => {
    onFilterChange({ btcSignal: signal });
  };
  const signalLabel = assetType === 'crypto' ? 'USD Signal' : 'Signal';
  const signalValue =
    'usdSignal' in filterConfig ? filterConfig.usdSignal : 'all';

  return (
    <div className="filter-drawer-content">
      <div className="filter-section">
        <label htmlFor="mobile-signal">{signalLabel}</label>
        <select
          id="mobile-signal"
          value={signalValue}
          onChange={e => {
            const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
            handleUsdSignalChange(value);
          }}
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>

      <div className="filter-section">
        <label htmlFor="mobile-btc-signal">BTC Signal</label>
        <select
          id="mobile-btc-signal"
          value={'btcSignal' in filterConfig ? filterConfig.btcSignal : 'all'}
          onChange={e => {
            const value = e.target.value as 'all' | 'gold' | 'blue' | 'gray';
            handleBtcSignalChange(value);
          }}
          className="filter-drawer-select"
        >
          <option value="all">All Signals</option>
          <option value="gold">🟡 Gold (Bullish)</option>
          <option value="blue">🔵 Blue (Bearish)</option>
          <option value="gray">⚪ Gray (Neutral)</option>
        </select>
      </div>
    </div>
  );
};
