import React from "react";

import { useTableData, useTableFiltering, useTableState } from '@/hooks/useTableContext';

interface FilterDrawerFooterProps {
  onClose: () => void;
}

export const FilterDrawerFooter: React.FC<FilterDrawerFooterProps> = ({
  onClose,
}) => {
  const { hasActiveFilters, onClearFilters } = useTableFiltering();
  const { filteredCount, totalCount } = useTableData();
  const { assetType } = useTableState();
  return (
    <div className="filter-drawer-footer">
      <div className="filter-results-mobile">
        Showing {filteredCount} of {totalCount} {assetType === 'crypto' ? 'cryptocurrencies' : 'stocks'}
      </div>

      <div className="filter-drawer-actions">
        {hasActiveFilters && (
          <button onClick={onClearFilters} className="filter-drawer-clear">
            Clear All
          </button>
        )}
        <button onClick={onClose} className="filter-drawer-apply">
          Apply Filters
        </button>
      </div>
    </div>
  );
};
