import React, { useState } from 'react';

import { useTableAssetType, useTableInteractions } from '@/hooks/useTableContext';
import { navigation } from '@/utils/formatters';

import { CardDetails } from './CardDetails';
import { CardHeader } from './CardHeader';
import { CardSignals } from './CardSignals';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated/index';

// Component-specific styles
import '../../styles/layout/mobile-components.css';

interface CryptoCardProps {
  crypto: CryptoCurrencyStatisticsDto | StockStatisticsDto;
  btcData?: IndicatorValueDto;
}

// eslint-disable-next-line max-lines-per-function
export const Card: React.FC<CryptoCardProps> = ({
  crypto,
  btcData,
}) => {
  const { formatDate, assetType } = useTableAssetType();
  const { onSignalClick } = useTableInteractions();
  const [isExpanded, setIsExpanded] = useState(false);
  const usdData = crypto.indicatorValues.find(Boolean);

  const usdTooltip = `Click to view chart${
    usdData?.timestamp !== null && usdData?.timestamp !== undefined
      ? ` • Last update: ${formatDate(usdData.timestamp)}`
      : ''
  }`;
  const btcTooltip = `Click to view chart${
    btcData?.timestamp !== null && btcData?.timestamp !== undefined
      ? ` • Last update: ${formatDate(btcData.timestamp)}`
      : ''
  }`;

  const handleSymbolClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent card expansion
    if (
      assetType === 'crypto' &&
      'mapping' in crypto &&
      'slug' in crypto.mapping &&
      typeof crypto.mapping.slug === 'string'
    ) {
      navigation.openCoinMarketCap(crypto.mapping.slug);
    } else {
      navigation.openYahoo(crypto?.symbol);
    }
  };

  const handleCardClick = () => {
    setIsExpanded(!isExpanded);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ' ') {
      e.preventDefault();
      handleCardClick();
    }
  };

  return (
    <div
      className="crypto-card"
      onClick={handleCardClick}
      onKeyDown={handleKeyDown}
      role="button"
      tabIndex={0}
      aria-expanded={isExpanded}
      aria-label={`${crypto.symbol} cryptocurrency card. Click to ${
        isExpanded ? 'collapse' : 'expand'
      } details.`}
    >
      <CardHeader
        crypto={crypto}
        usdData={usdData}
        onSymbolClick={handleSymbolClick}
        assetType={assetType}
      />

      <CardSignals
        symbol={crypto.symbol}
        usdData={usdData}
        btcData={btcData}
        usdTooltip={usdTooltip}
        btcTooltip={btcTooltip}
        onSignalClick={onSignalClick}
        assetType={assetType}
        crypto={crypto}
      />

      {isExpanded && (
        <CardDetails
          usdData={usdData}
          btcData={btcData}
          formatDate={formatDate}
        />
      )}

      <div className="crypto-card-expand-indicator">
        <span className={`expand-icon ${isExpanded ? 'expanded' : ''}`}>▼</span>
      </div>
    </div>
  );
};
