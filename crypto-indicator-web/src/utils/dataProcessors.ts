import { CURRENCIES, DATA_PROCESSING } from '@/constants/app';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockStatisticsDto,
} from '@/generated';

export const filterByCurrency = (
  statistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
  currency: string,
): (CryptoCurrencyStatisticsDto | StockStatisticsDto)[] => {
  return statistics.filter(crypto => crypto.conversionCurrency === currency);
};

export const findBtcDataForSymbol = (
  btcStatistics: CryptoCurrencyStatisticsDto[] | StockStatisticsDto[],
  symbol: string,
): IndicatorValueDto | undefined => {
  return btcStatistics
    .find(el => el.symbol === symbol)
    ?.indicatorValues?.find(el => el);
};

export const formatDate = (isoString?: string): string => {
  if (isoString === null || isoString === undefined || isoString === '') {
    return DATA_PROCESSING.DEFAULT_VALUE;
  }
  return new Date(isoString).toLocaleDateString();
};

export const processCryptoStatistics = (
  statistics: CryptoCurrencyStatisticsDto[],
) => {
  const usdStatistics = filterByCurrency(statistics, CURRENCIES.USD).sort(
    (a, b) => a.symbol.localeCompare(b.symbol),
  );
  const btcStatistics = filterByCurrency(statistics, CURRENCIES.BTC);

  return {
    usdStatistics: usdStatistics as CryptoCurrencyStatisticsDto[],
    btcStatistics: btcStatistics as CryptoCurrencyStatisticsDto[],
    totalCount: usdStatistics.length,
  };
};
