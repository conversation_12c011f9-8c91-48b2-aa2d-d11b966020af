import { findBtcDataForSymbol } from '@/utils/dataProcessors';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockIndicatorValueDto,
  StockStatisticsDto} from '@/generated';
import type { AssetType,SortColumn, SortConfig, SortDirection } from '@/types/table';

// Union types for generic processing
export type AssetStatisticsDto = CryptoCurrencyStatisticsDto | StockStatisticsDto;
export type AssetIndicatorValueDto = IndicatorValueDto | StockIndicatorValueDto;

// Signal priority mapping
const SIGNAL_PRIORITY = {
  gold: 3,
  blue: 2,
  gray: 1,
  null: 0,
  undefined: 0,
} as const;

/**
 * Get signal priority for sorting
 */
export const getSignalPriority = (color: string | null | undefined): number => {
  if (color === null || color === undefined || color === '') {
    return SIGNAL_PRIORITY.null;
  }
  return SIGNAL_PRIORITY[color as keyof typeof SIGNAL_PRIORITY] || SIGNAL_PRIORITY.null;
};

/**
 * Get the latest indicator data for any asset type
 */
const getLatestAssetData = (asset: AssetStatisticsDto): AssetIndicatorValueDto | undefined => {
  if (asset.indicatorValues.length === 0) {
    return undefined;
  }
  return asset.indicatorValues.at(-1);
};

interface SortValueConfig {
  asset: AssetStatisticsDto;
  column: SortColumn;
  latestData: AssetIndicatorValueDto | undefined;
  btcStatistics: AssetStatisticsDto[];
  assetType: AssetType;
}

/**
 * Get sort value for any asset type based on column
 */
const getSortValue = (config: SortValueConfig): string | number => {
  const { asset, column, latestData, btcStatistics, assetType } = config;
  switch (column) {
    case 'symbol': {
      return asset.symbol;
    }
    case 'usdPrice': {
      return latestData?.close ?? 0;
    }
    case 'marketCap': {
      // For crypto: use marketCap, for stock: use volume
      if (assetType === 'crypto') {
        return (latestData as IndicatorValueDto)?.marketCap ?? 0;
      }
      return latestData?.volume ?? 0;
    }
    case 'usdSignal': {
      return getSignalPriority(latestData?.color);
    }
    case 'btcPrice': {
      const btcData = findBtcDataForSymbol(btcStatistics, asset.symbol);
      return btcData?.close ?? 0;
    }
    case 'btcSignal': {
      const btcData = findBtcDataForSymbol(btcStatistics, asset.symbol);
      return getSignalPriority(btcData?.color);
    }
    default: {
      return '';
    }
  }
};

/**
 * Handle null/undefined values in comparison
 */
const handleNullValues = (
  aValue: string | number | null | undefined,
  bValue: string | number | null | undefined,
  direction: SortDirection,
): number | null => {
  if ((aValue === null || aValue === undefined) && (bValue === null || bValue === undefined)) {
    return 0;
  }
  if (aValue === null || aValue === undefined) {
    return direction === 'asc' ? 1 : -1;
  }
  if (bValue === null || bValue === undefined) {
    return direction === 'asc' ? -1 : 1;
  }
  return null; // No null values, continue with normal comparison
};

/**
 * Compare two non-null values
 */
const compareNonNullValues = (aValue: string | number, bValue: string | number): number => {
  if (typeof aValue === 'string' && typeof bValue === 'string') {
    return aValue.localeCompare(bValue);
  }
  if (typeof aValue === 'number' && typeof bValue === 'number') {
    return aValue - bValue;
  }
  // Handle mixed types
  return String(aValue).localeCompare(String(bValue));
};

/**
 * Compare two values for sorting
 */
const compareValues = (
  aValue: string | number,
  bValue: string | number,
  direction: SortDirection,
): number => {
  if (direction === null) {
    return 0;
  }

  // Handle null/undefined values first
  const nullResult = handleNullValues(aValue, bValue, direction);
  if (nullResult !== null) {
    return nullResult;
  }

  // Compare non-null values
  const result = compareNonNullValues(aValue, bValue);
  return direction === 'asc' ? result : -result;
};

/**
 * Generic sorting function for any asset type
 * Replaces applySorting and applyStockSorting with a single implementation
 */
export const applyAssetSorting = <T extends AssetStatisticsDto>(
  data: T[],
  btcStatistics: T[],
  sortConfig: SortConfig,
  assetType: AssetType
): T[] => {
  if (!sortConfig.column || !sortConfig.direction) {
    return data;
  }

  return [...data].sort((a, b) => {
    const aData = getLatestAssetData(a);
    const bData = getLatestAssetData(b);

    if (!sortConfig.column || !sortConfig.direction) {
      return 0;
    }

    const aValue = getSortValue({
      asset: a,
      column: sortConfig.column,
      latestData: aData,
      btcStatistics,
      assetType,
    });
    const bValue = getSortValue({
      asset: b,
      column: sortConfig.column,
      latestData: bData,
      btcStatistics,
      assetType,
    });

    const primaryResult = compareValues(aValue, bValue, sortConfig.direction);

    // Secondary sort by symbol for stable sorting
    if (primaryResult === 0) {
      return a.symbol.localeCompare(b.symbol);
    }

    return primaryResult;
  });
};

/**
 * Convenience functions for backward compatibility
 */
export const applySorting = (
  data: CryptoCurrencyStatisticsDto[],
  btcStatistics: CryptoCurrencyStatisticsDto[],
  sortConfig: SortConfig,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined
): CryptoCurrencyStatisticsDto[] => {
  return applyAssetSorting(data, btcStatistics, sortConfig, 'crypto');
};

export const applyStockSorting = (
  data: StockStatisticsDto[],
  btcStatistics: StockStatisticsDto[],
  sortConfig: SortConfig,
): StockStatisticsDto[] => {
  return applyAssetSorting(data, btcStatistics, sortConfig, 'stock');
};
