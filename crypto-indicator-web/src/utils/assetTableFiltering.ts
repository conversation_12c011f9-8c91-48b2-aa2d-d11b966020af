import { findBtcDataForSymbol } from '@/utils/dataProcessors';

import type {
  CryptoCurrencyStatisticsDto,
  IndicatorValueDto,
  StockIndicatorValueDto,
  StockStatisticsDto} from '@/generated';
import type { FilterConfig } from '@/types/table';

// Union types for generic processing
export type AssetStatisticsDto = CryptoCurrencyStatisticsDto | StockStatisticsDto;
export type AssetIndicatorValueDto = IndicatorValueDto | StockIndicatorValueDto;

/**
 * Check if symbol matches search criteria
 */
export const matchesSymbolSearch = (symbol: string, search: string): boolean => {
  if (!search.trim()) {
    return true;
  }
  return symbol.toLowerCase().includes(search.toLowerCase().trim());
};

/**
 * Check if asset name matches search criteria (for assets that have mapping.name)
 */
const matchesNameSearch = (asset: AssetStatisticsDto, search: string): boolean => {
  if (!search.trim()) {
    return true;
  }
  const searchTerm = search.toLowerCase();
  const name = asset.mapping?.name?.toLowerCase();
  return name ? name.includes(searchTerm) : false;
};

/**
 * Check if signal color matches filter
 */
export const matchesSignalFilter = (color: string | null | undefined, filter: string): boolean => {
  if (filter === 'all') {
    return true;
  }
  return color === filter;
};

/**
 * Get the latest indicator data for any asset type
 */
const getLatestAssetData = (asset: AssetStatisticsDto): AssetIndicatorValueDto | undefined => {
  if (asset.indicatorValues.length === 0) {
    return undefined;
  }
  return asset.indicatorValues.at(-1);
};

/**
 * Generic filtering function for any asset type
 * Replaces applyFilters and applyStockFilters with a single implementation
 */
export const applyAssetFilters = <T extends AssetStatisticsDto>(
  data: T[],
  btcStatistics: T[],
  filterConfig: FilterConfig
): T[] => {
  return data.filter((asset) => {
    const latestData = getLatestAssetData(asset);
    const btcData = findBtcDataForSymbol(btcStatistics, asset.symbol);

    // Symbol search filter
    if (filterConfig.symbolSearch) {
      const symbolMatch = matchesSymbolSearch(asset.symbol, filterConfig.symbolSearch);
      const nameMatch = matchesNameSearch(asset, filterConfig.symbolSearch);

      if (!symbolMatch && !nameMatch) {
        return false;
      }
    }

    // USD signal filter
    if (!matchesSignalFilter(latestData?.color, filterConfig.usdSignal)) {
      return false;
    }

    // BTC signal filter
    return matchesSignalFilter(btcData?.color, filterConfig.btcSignal);
  });
};

/**
 * Convenience functions for backward compatibility
 */
export const applyFilters = (
  data: CryptoCurrencyStatisticsDto[],
  btcStatistics: CryptoCurrencyStatisticsDto[],
  filterConfig: FilterConfig,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _findBtcDataForSymbol: (btcStats: CryptoCurrencyStatisticsDto[], symbol: string) => IndicatorValueDto | undefined
): CryptoCurrencyStatisticsDto[] => {
  return applyAssetFilters(data, btcStatistics, filterConfig);
};

export const applyStockFilters = (
  data: StockStatisticsDto[],
  btcStatistics: StockStatisticsDto[],
  filterConfig: FilterConfig,
): StockStatisticsDto[] => {
  return applyAssetFilters(data, btcStatistics, filterConfig);
};
